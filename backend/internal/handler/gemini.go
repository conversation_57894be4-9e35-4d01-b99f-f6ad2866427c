package handler

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"interviewmaster/internal/config"
	"interviewmaster/internal/utils"
)

// GeminiHandler Gemini处理器
type GeminiHandler struct {
	ctx context.Context
}

// NewGeminiHandler 创建Gemini处理器实例
func NewGeminiHandler(ctx context.Context) *GeminiHandler {
	return &GeminiHandler{
		ctx: ctx,
	}
}

// CreateEphemeralTokenRequest 创建临时令牌请求
type CreateEphemeralTokenRequest struct {
	Domain        string `json:"domain" binding:"required"` // 面试领域
	PromptVersion string `json:"prompt_version"`            // 提示词版本，可选
	Uses          int    `json:"uses"`                      // 使用次数，默认1
	ExpireMinutes int    `json:"expire_minutes"`            // 过期时间（分钟），默认30
}

// CreateEphemeralTokenResponse 创建临时令牌响应
type CreateEphemeralTokenResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	Uses      int       `json:"uses"`
}

// CreateEphemeralToken 创建临时令牌
func (h *GeminiHandler) CreateEphemeralToken(c *gin.Context) {
	// 获取用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "用户未认证")
		return
	}

	var req CreateEphemeralTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Uses == 0 {
		req.Uses = 1
	}
	if req.ExpireMinutes == 0 {
		req.ExpireMinutes = 30
	}

	// 验证参数
	if req.ExpireMinutes > 60 {
		utils.ValidationErrorResponse(c, "过期时间不能超过60分钟")
		return
	}

	// 调用Gemini API创建临时令牌
	token, err := h.createGeminiEphemeralToken(userID.(uint64), &req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "创建临时令牌失败: "+err.Error())
		return
	}

	utils.SuccessResponse(c, token)
}

// createGeminiEphemeralToken 调用Gemini API创建临时令牌
func (h *GeminiHandler) createGeminiEphemeralToken(userID uint64, req *CreateEphemeralTokenRequest) (*CreateEphemeralTokenResponse, error) {
	cfg := config.GlobalConfig
	if cfg == nil {
		return nil, fmt.Errorf("配置未初始化")
	}

	// 计算过期时间
	expireTime := time.Now().Add(time.Duration(req.ExpireMinutes) * time.Minute)
	newSessionExpireTime := time.Now().Add(1 * time.Minute) // 新会话过期时间1分钟

	// 构建请求体
	requestBody := map[string]interface{}{
		"config": map[string]interface{}{
			"uses":                    req.Uses,
			"expire_time":             expireTime.Format(time.RFC3339),
			"new_session_expire_time": newSessionExpireTime.Format(time.RFC3339),
		},
	}

	// 如果指定了领域和提示词版本，添加约束
	if req.Domain != "" {
		systemInstruction := h.buildSystemInstruction(req.Domain, req.PromptVersion)
		requestBody["config"].(map[string]interface{})["live_connect_constraints"] = map[string]interface{}{
			"model": cfg.Gemini.Model,
			"config": map[string]interface{}{
				"system_instruction":  systemInstruction,
				"response_modalities": []string{"AUDIO"},
				"session_resumption":  map[string]interface{}{},
				"temperature":         0.7,
			},
		}
	}

	// 序列化请求体
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	// 创建HTTP请求
	url := fmt.Sprintf("%s/v1alpha/authTokens", cfg.Gemini.BaseURL)
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("x-goog-api-key", cfg.Gemini.APIKey)

	// 发送请求
	client := &http.Client{
		Timeout: time.Duration(cfg.Gemini.Timeout) * time.Second,
	}
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Gemini API返回错误状态: %d", resp.StatusCode)
	}

	// 解析响应
	var geminiResp struct {
		Name string `json:"name"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&geminiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 返回结果
	return &CreateEphemeralTokenResponse{
		Token:     geminiResp.Name,
		ExpiresAt: expireTime,
		Uses:      req.Uses,
	}, nil
}

// buildSystemInstruction 构建系统指令
func (h *GeminiHandler) buildSystemInstruction(domain, promptVersion string) string {
	// 基础指令
	baseInstruction := `你是一个专业的面试助手，专门帮助面试者回答技术面试问题。

请遵循以下原则：
1. 提供准确、专业的技术回答
2. 回答要简洁明了，重点突出
3. 适当举例说明复杂概念
4. 保持自信和专业的语调
5. 如果不确定答案，诚实说明并提供相关思路

请用中文回答所有问题。`

	// 根据领域添加专业指令
	var domainInstruction string
	switch domain {
	case "前端开发":
		domainInstruction = "\n\n你现在专注于前端开发面试，重点关注JavaScript、React、Vue、CSS、HTML5、性能优化、工程化等前端技术。"
	case "后端开发":
		domainInstruction = "\n\n你现在专注于后端开发面试，重点关注服务器架构、数据库设计、API开发、微服务、缓存、消息队列等后端技术。"
	case "算法与数据结构":
		domainInstruction = "\n\n你现在专注于算法与数据结构面试，重点关注时间复杂度、空间复杂度、常见算法思路、数据结构选择等。"
	case "系统设计":
		domainInstruction = "\n\n你现在专注于系统设计面试，重点关注高可用、高并发、分布式系统、负载均衡、数据一致性等系统架构问题。"
	default:
		domainInstruction = "\n\n你现在专注于技术面试，涵盖编程语言、框架、数据库、系统设计等各个技术领域。"
	}

	// 根据A/B测试版本调整
	var versionInstruction string
	if promptVersion == "B" {
		versionInstruction = "\n\n请在回答中更多地包含实际项目经验和最佳实践案例。"
	}

	return baseInstruction + domainInstruction + versionInstruction
}
