package main

import (
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"

	"interviewmaster/internal/config"
	"interviewmaster/internal/handler"
	"interviewmaster/internal/middleware"
	"interviewmaster/internal/model"
	"interviewmaster/internal/service"
	"interviewmaster/internal/utils"
	"interviewmaster/pkg/database"
	"interviewmaster/pkg/redis"
	"interviewmaster/pkg/websocket"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("config.json")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		log.Fatalf("初始化MySQL失败: %v", err)
	}
	defer database.CloseMySQL()

	// 初始化Redis
	if err := redis.InitRedis(&cfg.Database.Redis); err != nil {
		log.Fatalf("初始化Redis失败: %v", err)
	}
	defer redis.CloseRedis()

	// 自动迁移数据库
	if err := model.AutoMigrate(database.GetDB()); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}

	// 初始化默认数据
	if err := model.InitDefaultData(database.GetDB()); err != nil {
		log.Fatalf("初始化默认数据失败: %v", err)
	}

	// 初始化WebSocket Hub
	websocket.InitWebSocketHub(cfg.WebSocket.MaxConnections)

	// 初始化GeminiService
	geminiService := service.NewGeminiService()

	// 设置WebSocket回调函数
	websocket.SetAudioInputHandler(func(userID uint64, sessionID string, audioData interface{}) error {
		log.Printf("开始处理用户 %d 的音频数据，数据类型: %T", userID, audioData)

		// 处理音频数据，这里需要从audioData中提取base64编码的音频数据
		audioMap, ok := audioData.(map[string]interface{})
		if !ok {
			log.Printf("音频数据格式错误，期望map[string]interface{}，实际: %T", audioData)
			return fmt.Errorf("无效的音频数据格式")
		}

		log.Printf("音频数据字段: %+v", audioMap)

		// 提取base64数据
		base64Data, ok := audioMap["data"].(string)
		if !ok {
			log.Printf("缺少data字段或类型错误，data字段类型: %T", audioMap["data"])
			return fmt.Errorf("无效的音频数据格式：缺少data字段")
		}

		// 提取格式信息
		format, ok := audioMap["format"].(string)
		if !ok {
			format = "wav" // 默认格式
			log.Printf("使用默认音频格式: %s", format)
		}

		log.Printf("准备解码base64数据，长度: %d", len(base64Data))

		// 解码base64数据
		audioBytes, err := base64.StdEncoding.DecodeString(base64Data)
		if err != nil {
			log.Printf("解码base64数据失败: %v", err)
			return fmt.Errorf("解码音频数据失败: %v", err)
		}

		log.Printf("收到用户 %d 的音频数据，大小: %d bytes, 格式: %s", userID, len(audioBytes), format)

		// 调用GeminiService处理音频
		log.Printf("调用GeminiService处理音频")
		err = geminiService.ProcessAudioInput(userID, sessionID, audioBytes, format)
		if err != nil {
			log.Printf("GeminiService处理音频失败: %v", err)
		} else {
			log.Printf("GeminiService处理音频成功")
		}
		return err
	})

	websocket.SetInterruptHandler(func(userID uint64, sessionID string) error {
		return geminiService.InterruptSession(userID, sessionID)
	})

	websocket.SetSessionStartHandler(func(userID uint64) (string, error) {
		// 默认使用"技术面试"领域
		return geminiService.StartInterviewSession(userID, "技术面试")
	})

	// 启动WebSocket Hub
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	go websocket.GlobalHub.Run(ctx)

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建Gin引擎
	r := gin.Default()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 创建处理器实例
	authHandler := handler.NewAuthHandler()
	userHandler := handler.NewUserHandler()
	wsHandler := handler.NewWebSocketHandler(ctx)
	paymentHandler := handler.NewPaymentHandler()

	// 基础路由
	r.GET("/", func(c *gin.Context) {
		utils.SuccessResponse(c, gin.H{
			"message": "面试官见招拆招App API服务",
			"version": "1.0.0",
			"status":  "running",
		})
	})

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		utils.SuccessResponse(c, gin.H{
			"status": "ok",
			"time":   time.Now().Unix(),
		})
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		// 认证相关路由（无需认证）
		auth := api.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/sms", authHandler.SendSMS)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.POST("/logout", authHandler.Logout)
			auth.GET("/verify", authHandler.VerifyToken)
		}

		// 用户相关路由（需要认证）
		user := api.Group("/user")
		user.Use(middleware.AuthMiddleware())
		{
			user.GET("/info", userHandler.GetUserInfo)
			user.PUT("/info", userHandler.UpdateUserInfo)
			user.GET("/balance", userHandler.GetUserBalance)
			user.POST("/consume", userHandler.ConsumeUserBalance)
		}

		// 管理员用户路由（需要管理员认证）
		adminUser := api.Group("/admin/user")
		adminUser.Use(middleware.AdminAuthMiddleware())
		{
			adminUser.GET("/list", userHandler.GetUserList)
			adminUser.PUT("/:id/balance", userHandler.UpdateUserBalance)
			adminUser.DELETE("/:id", userHandler.DeleteUser)
		}

		// WebSocket管理路由（需要管理员认证）
		adminWS := api.Group("/admin/websocket")
		adminWS.Use(middleware.AdminAuthMiddleware())
		{
			adminWS.GET("/stats", wsHandler.GetConnectionStats)
			adminWS.POST("/send", wsHandler.SendMessageToUser)
			adminWS.POST("/broadcast", wsHandler.BroadcastMessage)
			adminWS.POST("/check-online", wsHandler.CheckUserOnline)
			adminWS.POST("/close-connection", wsHandler.CloseUserConnection)
		}

		// 管理员订单路由（需要管理员认证）
		adminOrder := api.Group("/admin/order")
		adminOrder.Use(middleware.AdminAuthMiddleware())
		{
			adminOrder.GET("/list", paymentHandler.AdminGetOrders)
			adminOrder.PUT("/:order_no/status", paymentHandler.AdminUpdateOrderStatus)
		}

		// 商品相关路由（无需认证）
		product := api.Group("/product")
		{
			product.GET("/list", paymentHandler.GetProducts)
		}

		// 订单相关路由（需要认证）
		order := api.Group("/order")
		order.Use(middleware.AuthMiddleware())
		{
			order.POST("/create", paymentHandler.CreateOrder)
			order.GET("/list", paymentHandler.GetUserOrders)
			order.GET("/:order_no", paymentHandler.GetOrderDetail)
			order.GET("/:order_no/status", paymentHandler.GetOrderStatus)
			order.POST("/:order_no/cancel", paymentHandler.CancelOrder)
		}

		// 支付相关路由（需要认证）
		payment := api.Group("/payment")
		payment.Use(middleware.AuthMiddleware())
		{
			payment.GET("/methods", paymentHandler.GetPaymentMethods)
			payment.POST("/wechat/pay", paymentHandler.CreateWeChatPayment)
			payment.POST("/alipay/pay", paymentHandler.CreateAlipayPayment)
		}

		// 支付回调路由（无需认证）
		notify := api.Group("/notify")
		{
			notify.POST("/wechat", paymentHandler.WeChatNotify)
			notify.POST("/alipay", paymentHandler.AlipayNotify)
		}

		// 面试历史相关路由（需要认证）
		interview := api.Group("/interview")
		interview.Use(middleware.AuthMiddleware())
		{
			interview.GET("/history", func(c *gin.Context) {
				utils.SuccessResponseWithMessage(c, "面试历史接口", nil)
			})
			interview.POST("/feedback", func(c *gin.Context) {
				utils.SuccessResponseWithMessage(c, "用户反馈接口", nil)
			})
		}
	}

	// WebSocket路由
	r.GET("/ws/interview", wsHandler.HandleConnection)

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:         cfg.Server.Host + ":" + cfg.Server.Port,
		Handler:      r,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
	}

	// 启动服务器
	go func() {
		log.Printf("服务器启动在 %s:%s", cfg.Server.Host, cfg.Server.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 优雅停机
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("正在关闭服务器...")

	// 关闭WebSocket Hub
	cancel()

	// 关闭HTTP服务器
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer shutdownCancel()
	if err := srv.Shutdown(shutdownCtx); err != nil {
		log.Fatal("服务器强制关闭:", err)
	}

	log.Println("服务器已退出")
}
