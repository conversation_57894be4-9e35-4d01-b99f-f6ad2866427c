package websocket

import (
	"context"
	"encoding/json"
	"log"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"

	"interviewmaster/internal/config"
)

// AudioInputHandler 音频输入处理函数类型
type AudioInputHandler func(userID uint64, sessionID string, audioData interface{}) error

// InterruptHandler 中断处理函数类型
type InterruptHandler func(userID uint64, sessionID string) error

// Connection WebSocket连接结构
type Connection struct {
	ID                string            `json:"id"`
	UserID            uint64            `json:"user_id"`
	Phone             string            `json:"phone"`
	Conn              *websocket.Conn   `json:"-"`
	Send              chan []byte       `json:"-"`
	Hub               *Hub              `json:"-"`
	LastPing          time.Time         `json:"last_ping"`
	CreatedAt         time.Time         `json:"created_at"`
	IsAlive           bool              `json:"is_alive"`
	SessionID         string            `json:"session_id"`
	mutex             sync.RWMutex      `json:"-"`
	audioInputHandler AudioInputHandler `json:"-"`
	interruptHandler  InterruptHandler  `json:"-"`
}

// Message WebSocket消息结构
type Message struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data,omitempty"`
	SessionID string      `json:"session_id,omitempty"`
	Status    string      `json:"status,omitempty"`
	Timestamp int64       `json:"timestamp"`
}

// Hub WebSocket连接管理中心
type Hub struct {
	connections map[string]*Connection
	register    chan *Connection
	unregister  chan *Connection
	broadcast   chan []byte
	mutex       sync.RWMutex
	maxConns    int
}

// NewHub 创建新的Hub实例
func NewHub(maxConns int) *Hub {
	return &Hub{
		connections: make(map[string]*Connection),
		register:    make(chan *Connection),
		unregister:  make(chan *Connection),
		broadcast:   make(chan []byte),
		maxConns:    maxConns,
	}
}

// Run 启动Hub
func (h *Hub) Run(ctx context.Context) {
	log.Println("WebSocket Hub 启动")

	// 启动心跳检查
	go h.startHeartbeatChecker(ctx)

	for {
		select {
		case conn := <-h.register:
			h.registerConnection(conn)
		case conn := <-h.unregister:
			h.unregisterConnection(conn)
		case message := <-h.broadcast:
			h.broadcastMessage(message)
		case <-ctx.Done():
			log.Println("WebSocket Hub 停止")
			return
		}
	}
}

// registerConnection 注册连接
func (h *Hub) registerConnection(conn *Connection) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	// 检查连接数限制
	if len(h.connections) >= h.maxConns {
		log.Printf("连接数已达上限 %d，拒绝新连接", h.maxConns)
		conn.Conn.Close()
		return
	}

	// 如果用户已有连接，关闭旧连接
	for _, existingConn := range h.connections {
		if existingConn.UserID == conn.UserID {
			log.Printf("用户 %d 已有连接，关闭旧连接", conn.UserID)
			existingConn.Close()
			delete(h.connections, existingConn.ID)
			break
		}
	}

	h.connections[conn.ID] = conn
	log.Printf("用户 %d 连接成功，连接ID: %s，当前连接数: %d",
		conn.UserID, conn.ID, len(h.connections))

	// 发送连接成功消息
	conn.SendMessage(Message{
		Type:      "connected",
		Data:      gin.H{"connection_id": conn.ID},
		Timestamp: time.Now().Unix(),
	})
}

// unregisterConnection 注销连接
func (h *Hub) unregisterConnection(conn *Connection) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if _, ok := h.connections[conn.ID]; ok {
		delete(h.connections, conn.ID)
		close(conn.Send)
		log.Printf("用户 %d 断开连接，连接ID: %s，当前连接数: %d",
			conn.UserID, conn.ID, len(h.connections))
	}
}

// broadcastMessage 广播消息
func (h *Hub) broadcastMessage(message []byte) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for _, conn := range h.connections {
		select {
		case conn.Send <- message:
		default:
			// 发送失败，关闭连接
			conn.Close()
			delete(h.connections, conn.ID)
		}
	}
}

// GetConnection 获取连接
func (h *Hub) GetConnection(connID string) *Connection {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return h.connections[connID]
}

// GetUserConnection 获取用户连接
func (h *Hub) GetUserConnection(userID uint64) *Connection {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for _, conn := range h.connections {
		if conn.UserID == userID {
			return conn
		}
	}
	return nil
}

// GetConnectionCount 获取连接数
func (h *Hub) GetConnectionCount() int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return len(h.connections)
}

// startHeartbeatChecker 启动心跳检查
func (h *Hub) startHeartbeatChecker(ctx context.Context) {
	cfg := config.GlobalConfig
	if cfg == nil {
		return
	}

	ticker := time.NewTicker(cfg.WebSocket.GetHeartbeatInterval())
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			h.checkHeartbeat()
		case <-ctx.Done():
			return
		}
	}
}

// checkHeartbeat 检查心跳
func (h *Hub) checkHeartbeat() {
	cfg := config.GlobalConfig
	if cfg == nil {
		return
	}

	h.mutex.RLock()
	deadConnections := make([]*Connection, 0)

	for _, conn := range h.connections {
		// 给新连接一些缓冲时间，避免刚连接就被认为超时
		connectionAge := time.Since(conn.CreatedAt)
		if connectionAge < cfg.WebSocket.GetReadTimeout() {
			continue
		}

		if time.Since(conn.LastPing) > cfg.WebSocket.GetReadTimeout() {
			deadConnections = append(deadConnections, conn)
		}
	}
	h.mutex.RUnlock()

	// 关闭死连接
	for _, conn := range deadConnections {
		log.Printf("连接 %s 心跳超时，关闭连接", conn.ID)
		conn.Close()
	}
}

// SendMessage 发送消息
func (c *Connection) SendMessage(msg Message) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.IsAlive {
		return nil
	}

	msg.Timestamp = time.Now().Unix()
	data, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	select {
	case c.Send <- data:
		return nil
	default:
		return nil
	}
}

// Close 关闭连接
func (c *Connection) Close() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.IsAlive {
		c.IsAlive = false
		c.Conn.Close()
		c.Hub.unregister <- c
	}
}

// UpdatePing 更新心跳时间
func (c *Connection) UpdatePing() {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.LastPing = time.Now()
}

// readPump 读取消息
func (c *Connection) readPump() {
	defer func() {
		c.Close()
	}()

	cfg := config.GlobalConfig
	if cfg != nil {
		c.Conn.SetReadLimit(int64(cfg.WebSocket.BufferSize))
		c.Conn.SetReadDeadline(time.Now().Add(cfg.WebSocket.GetReadTimeout()))
		c.Conn.SetPongHandler(func(string) error {
			c.UpdatePing()
			c.Conn.SetReadDeadline(time.Now().Add(cfg.WebSocket.GetReadTimeout()))
			return nil
		})
	}

	for {
		_, message, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket错误: %v", err)
			}
			break
		}

		// 处理接收到的消息
		c.handleMessage(message)
	}
}

// writePump 写入消息
func (c *Connection) writePump() {
	cfg := config.GlobalConfig
	var ticker *time.Ticker

	if cfg != nil {
		ticker = time.NewTicker(cfg.WebSocket.GetHeartbeatInterval())
	} else {
		ticker = time.NewTicker(30 * time.Second)
	}
	defer ticker.Stop()

	defer func() {
		c.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			if cfg != nil {
				c.Conn.SetWriteDeadline(time.Now().Add(cfg.WebSocket.GetWriteTimeout()))
			}

			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
				log.Printf("写入消息失败: %v", err)
				return
			}

		case <-ticker.C:
			if cfg != nil {
				c.Conn.SetWriteDeadline(time.Now().Add(cfg.WebSocket.GetWriteTimeout()))
			}

			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理接收到的消息
func (c *Connection) handleMessage(data []byte) {
	var msg Message
	if err := json.Unmarshal(data, &msg); err != nil {
		log.Printf("解析消息失败: %v", err)
		return
	}

	// 更新心跳时间
	c.UpdatePing()

	// 根据消息类型处理
	switch msg.Type {
	case "ping":
		c.SendMessage(Message{Type: "pong"})
	case "audio_input":
		// 处理音频输入
		c.handleAudioInput(msg)
	case "interrupt":
		// 处理打断信号
		c.handleInterrupt(msg)
	default:
		log.Printf("未知消息类型: %s", msg.Type)
	}
}

// handleAudioInput 处理音频输入
func (c *Connection) handleAudioInput(msg Message) {
	log.Printf("收到用户 %d 的音频数据，消息类型: %s", c.UserID, msg.Type)
	log.Printf("消息数据类型: %T", msg.Data)

	// 如果没有会话ID，先发送错误状态
	if c.SessionID == "" {
		log.Printf("用户 %d 没有活跃的面试会话", c.UserID)
		c.SendMessage(Message{
			Type:   "status_update",
			Status: "error",
			Data:   map[string]interface{}{"error": "请先开始面试会话"},
		})
		return
	}

	// 调用音频处理回调
	if c.audioInputHandler != nil {
		log.Printf("调用音频处理回调，用户: %d, 会话: %s", c.UserID, c.SessionID)
		err := c.audioInputHandler(c.UserID, c.SessionID, msg.Data)
		if err != nil {
			log.Printf("音频处理失败: %v", err)
			c.SendMessage(Message{
				Type:   "status_update",
				Status: "error",
				Data:   map[string]interface{}{"error": "音频处理失败"},
			})
		} else {
			log.Printf("音频处理成功")
		}
	} else {
		log.Printf("音频处理回调未设置")
		c.SendMessage(Message{
			Type:   "status_update",
			Status: "error",
			Data:   map[string]interface{}{"error": "服务暂时不可用"},
		})
	}
}

// handleInterrupt 处理打断信号
func (c *Connection) handleInterrupt(msg Message) {
	log.Printf("用户 %d 发送打断信号", c.UserID)

	// 调用中断处理回调
	if c.interruptHandler != nil && c.SessionID != "" {
		err := c.interruptHandler(c.UserID, c.SessionID)
		if err != nil {
			log.Printf("中断处理失败: %v", err)
		}
	}

	// 发送中断状态
	c.SendMessage(Message{
		Type:   "status_update",
		Status: "interrupted",
	})
}

// SetAudioInputHandler 设置音频输入处理回调
func (c *Connection) SetAudioInputHandler(handler AudioInputHandler) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.audioInputHandler = handler
}

// SetInterruptHandler 设置中断处理回调
func (c *Connection) SetInterruptHandler(handler InterruptHandler) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.interruptHandler = handler
}

// SetSessionID 设置会话ID
func (c *Connection) SetSessionID(sessionID string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.SessionID = sessionID
}
