package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"

	"interviewmaster/internal/utils"
	"interviewmaster/pkg/jwt"
)

var (
	upgrader = websocket.Upgrader{
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		CheckOrigin: func(r *http.Request) bool {
			// 在生产环境中应该检查Origin
			return true
		},
	}

	// 全局Hub实例
	GlobalHub *Hub

	// 全局回调函数
	globalAudioInputHandler   AudioInputHandler
	globalInterruptHandler    InterruptHandler
	globalSessionStartHandler func(userID uint64) (string, error)
)

// InitWebSocketHub 初始化WebSocket Hub
func InitWebSocketHub(maxConns int) {
	GlobalHub = NewHub(maxConns)
}

// SetAudioInputHandler 设置全局音频输入处理回调
func SetAudioInputHandler(handler AudioInputHandler) {
	globalAudioInputHandler = handler
}

// SetInterruptHandler 设置全局中断处理回调
func SetInterruptHandler(handler InterruptHandler) {
	globalInterruptHandler = handler
}

// SetSessionStartHandler 设置全局会话开始处理回调
func SetSessionStartHandler(handler func(userID uint64) (string, error)) {
	globalSessionStartHandler = handler
}

// HandleWebSocket WebSocket连接处理器
func HandleWebSocket(c *gin.Context) {
	// 获取token参数
	token := c.Query("token")
	if token == "" {
		// 尝试从Header获取
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" && len(authHeader) > 7 && authHeader[:7] == "Bearer " {
			token = authHeader[7:]
		}
	}

	if token == "" {
		log.Printf("WebSocket连接失败: 缺少认证token")
		c.AbortWithStatus(http.StatusUnauthorized)
		return
	}

	// 验证token
	claims, err := jwt.ParseToken(token)
	if err != nil {
		log.Printf("WebSocket连接失败: 无效的token - %v", err)
		c.AbortWithStatus(http.StatusUnauthorized)
		return
	}

	// 升级HTTP连接为WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}

	// 创建连接实例
	now := time.Now()
	connection := &Connection{
		ID:        utils.GenerateSessionID(),
		UserID:    claims.UserID,
		Phone:     claims.Phone,
		Conn:      conn,
		Send:      make(chan []byte, 256),
		Hub:       GlobalHub,
		LastPing:  now,
		CreatedAt: now,
		IsAlive:   true,
	}

	// 设置回调函数
	connection.SetAudioInputHandler(globalAudioInputHandler)
	connection.SetInterruptHandler(globalInterruptHandler)

	// 注册连接
	GlobalHub.register <- connection

	// 自动开始面试会话
	if globalSessionStartHandler != nil {
		sessionID, err := globalSessionStartHandler(claims.UserID)
		if err != nil {
			log.Printf("自动开始面试会话失败: %v", err)
		} else {
			connection.SetSessionID(sessionID)
			log.Printf("用户 %d 自动开始面试会话: %s", claims.UserID, sessionID)
		}
	}

	// 启动读写协程
	go connection.writePump()
	go connection.readPump()
}

// BroadcastMessage 广播消息到所有连接
func BroadcastMessage(msg Message) {
	if GlobalHub == nil {
		return
	}

	data, err := json.Marshal(msg)
	if err != nil {
		log.Printf("序列化广播消息失败: %v", err)
		return
	}

	GlobalHub.broadcast <- data
}

// SendMessageToUser 发送消息给特定用户
func SendMessageToUser(userID uint64, msg Message) error {
	if GlobalHub == nil {
		return nil
	}

	conn := GlobalHub.GetUserConnection(userID)
	if conn == nil {
		return nil // 用户不在线
	}

	return conn.SendMessage(msg)
}

// SendMessageToConnection 发送消息给特定连接
func SendMessageToConnection(connID string, msg Message) error {
	if GlobalHub == nil {
		return nil
	}

	conn := GlobalHub.GetConnection(connID)
	if conn == nil {
		return nil // 连接不存在
	}

	return conn.SendMessage(msg)
}

// GetConnectionStats 获取连接统计信息
func GetConnectionStats() map[string]interface{} {
	if GlobalHub == nil {
		return map[string]interface{}{
			"total_connections": 0,
			"max_connections":   0,
		}
	}

	return map[string]interface{}{
		"total_connections": GlobalHub.GetConnectionCount(),
		"max_connections":   GlobalHub.maxConns,
	}
}

// CloseUserConnection 关闭用户连接
func CloseUserConnection(userID uint64) {
	if GlobalHub == nil {
		return
	}

	conn := GlobalHub.GetUserConnection(userID)
	if conn != nil {
		conn.Close()
	}
}

// IsUserOnline 检查用户是否在线
func IsUserOnline(userID uint64) bool {
	if GlobalHub == nil {
		return false
	}

	conn := GlobalHub.GetUserConnection(userID)
	return conn != nil && conn.IsAlive
}
